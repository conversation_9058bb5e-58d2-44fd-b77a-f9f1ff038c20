{"name": "client", "version": "1.1.0", "scripts": {"build": "vite build", "dev": "vite serve --port 5000", "test": "vitest run", "e2e": "npx playwright test", "e2e-headed": "npx playwright test --headed", "e2e-codegen": "npx playwright codegen localhost:5000", "e2e-install": "npx playwright install", "prettierApply": "npx prettier --write src", "prettierCheck": "npx prettier --check src"}, "devDependencies": {"@playwright/test": "1.55.0", "@sveltejs/vite-plugin-svelte": "6.1.3", "@tailwindcss/forms": "0.5.10", "@tailwindcss/postcss": "4.1.12", "@tailwindcss/vite": "4.1.12", "chance": "1.1.13", "dotenv": "17.2.1", "page": "1.11.6", "prettier": "3.6.2", "prettier-plugin-svelte": "3.4.0", "svelte": "5.38.3", "tailwindcss": "4.1.12", "uuid": "11.1.0", "vite": "7.1.3", "vitest": "3.2.4"}, "type": "module"}