:icons: font
:experimental: true
= Self-Contained System mit Spring-Boot und Svelte-UI

== Anwendungen

=== app/server

Backend-Server der Anwendung mit einer HSQL-Datenbank.

https://spring.io/projects/spring-boot

http://hsqldb.org

=== app/client

Browser-basierter Web-Client der Anwendung.

https://svelte.dev

https://tailwindcss.com

https://visionmedia.github.io/page.js

https://vitejs.dev

=== app/deploy

Lokales Deployment der Anwendung.

https://www.docker.com/

https://rancherdesktop.io/

== Bibliotheken

=== lib/backend-api

Datenmodell für den Backend-Server der Anwendung.

https://projectlombok.org/

https://github.com/FasterXML/jackson-docs

=== lib/backend-data

REST-API für den Backend-Server der Anwendung.

https://spring.io/projects/spring-framework

https://spring.io/projects/spring-restdocs

https://spring.io/projects/spring-data-jpa

https://spring.io/projects/spring-data-rest

https://docs.liquibase.com/home.html

== Versionierung

Die Versionierung der Anwendung basiert auf
https://git-scm.com/book/en/v2/Git-Basics-Tagging[_lightweight tags_]
im Repository und folgt dem Konzept 
https://semver.org/[_semantic versioning_]
mit Major und Minor-Nummer.
Eine Patch-Nummer wird nur für interne Zwecke verwendet.

[source, gradle]
----
./gradlew versionTag
----

Der Befehl zeigt die aktuelle Version der Anwendung.

Die Änderungshistorie basiert auf der
https://git-scm.com/book/en/v2/Git-Basics-Viewing-the-Commit-History[_commit history_]
des Repositories und folgt dem Konzept 
https://www.conventionalcommits.org/[_conventional commits_]
für die Beschreibung der Änderungen.

[source, gradle]
----
./gradlew versionRelease
----

Der Befehl zeigt eine Liste der Änderungen am aktuellen Release.
Die Liste kann auch leer sein.

[source, gradle]
----
./gradlew versionHistory
----

Der Befehl zeigt alle Änderungen der vergangenen Releases gruppiert nach der Version und absteigend sortiert.

Die aktuelle Version mit Major- und Minor-Nummer wird in der Datei `VERSION` eingetragen.

WARNING: Die Datei darf keinen Zeilenumbruch enthalten.

Zusätzlich muss die aktuelle Version in anderen Dateien eingetragen werden, z.B. in `package.json` oder `Chart.yaml`.

[source, gradle]
----
./gradlew versionCheck
----

Der Befehl prüft, ob alle eingetragenen Version mit Major- und Minor-Nummer exakt übereinstimmen.

== Häufig gestellte Fragen

=== Code bauen und testen

[source, npm]
----
npm install --prefix app/client
----

[source, gradle]
----
./gradlew clean build
----

[source, gradle]
----
./gradlew buildImage
----

=== Code formatieren

[source, gradle]
----
./gradlew format
----

=== Spring-Boot-Server lokal starten

[source, gradle]
----
./gradlew app:server:bootRun
----

Der Befehl startet den Server als Java-Anwendung.
Der HAL-Explorer wird im Browser mit `localhost:8080/api/explorer` aufgerufen.
Der GraphQl-Explorer wird im Browser mit `localhost:8080/api/graphiql` aufgerufen.

Mit der Umgebungsvariable `LOGGING_LEVEL_ORG_HIBERNATE_TYPE` kann die Ausgabe von Typinformationen gesteuert werden.
Mit dem Wert `TRACE` werden bspw. die aktuellen Parameter von Statements ausgegeben.

Mit der Umgebungsvariable `LOGGING_LEVEL_ORG_HIBERNATE_STAT` kann die Ausgabe von Statistiken gesteuert werden.
Mit dem Wert `DEBUG` werden bspw. die Kennzahlen von Statements ausgegeben.

[source, gradle]
----
./gradlew app:server:imageRun
----

Der Befehl startet den Server als Container-Image im Hintergrund (_detached_).
Der interaktive HAL-Explorer wird im Browser mit `localhost:8080/api/explorer` aufgerufen.
Der interaktive GraphQL-Explorer wird im Browser mit `localhost:8080/api/graphiql` aufgerufen.

[source, gradle]
----
./gradlew app:server:imageStop
----

Der Befehl beendet den Server.
Das Container-Image wird automatisch entfernt.

=== Svelte-Client lokal starten

[source, npm]
----
npm run dev --prefix app/client
----

Der Befehl startet den Client als Node-Anwendung.
Die Anwendung wird im Browser mit `localhost:5000` aufgerufen.

[source, gradle]
----
./gradlew app:client:imageRun
----

Der Befehl startet den Client als Container-Image im Hintergrund (_detached_).
Die Anwendung wird im Browser mit `localhost:5000` aufgerufen.

[source, gradle]
----
./gradlew app:client:imageStop
----

Der Befehl beendet den Client.
Das Container-Image wird automatisch entfernt.

=== Anwendung lokal starten

[source, gradle]
----
./gradlew composeup
----

Der Befehl startet Client und Server lokal.
Die Anwendung wird im Browser mit `localhost:5000` aufgerufen.

[source, gradle]
----
./gradlew composedown
----

Der Befehl stoppt Client und Server.
