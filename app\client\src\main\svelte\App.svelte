<script lang="ts">
  import "./index.css";
  import { fly } from "svelte/transition";
  import { quadIn } from "svelte/easing";
  import Toast from "./components/Toast";
  import Router from "./pager/Router.svelte";
  import Route from "./pager/Route.svelte";
  import RouteNotFound from "./pager/RouteNotFound.svelte";
  import AppHelp from "./AppHelp.svelte";
  import AppHome from "./AppHome.svelte";
  import AppLogo from "./AppLogo.svelte";
  import AppIcon from "./AppIcon.svelte";
  import Enum from "./pages/Enum.svelte";
  import Owner from "./pages/Owner.svelte";
  import OwnerViewer from "./pages/OwnerViewer.svelte";
  import Pet from "./pages/Pet.svelte";
  import PetViewer from "./pages/PetViewer.svelte";
  import Vet from "./pages/Vet.svelte";
  import VetViewer from "./pages/VetViewer.svelte";
  import Visit from "./pages/Visit.svelte";

  let menuVisible = $state(false);
  function handleClick() {
    menuVisible = false;
  }
</script>

<article class="flex flex-col h-screen">
  <header class="flex justify-between items-center bg-title-200 p-2 h-12">
    <nav class="flex flex-row text-lg text-title-600 gap-1">
      <AppIcon bind:open={menuVisible} />
      <AppLogo bind:open={menuVisible} />
    </nav>
    <nav class="flex flex-row text-lg text-title-600 gap-1">
      <a onclick={handleClick} href="/help">?</a>
    </nav>
  </header>
  <main class="flex-1 overflow-y-auto">
    <Toast />
    {#if menuVisible}
      <aside
        class="w-72 h-full pointer-events-none"
        transition:fly={{
          duration: 200,
          x: -300,
          easing: quadIn,
          opacity: 1,
        }}
      >
        <nav
          class="absolute flex w-full h-full pointer-events-auto z-10 bg-white"
        >
          <div class="w-full">
            <div class="flex flex-col p-2 text-gray-600 gap-1">
              <span class="text-lg text-gray-900 capitalize">Client</span>
              <div class="flex flex-col p-4 text-gray-600 gap-1">
                <a onclick={handleClick} href="/owner">Owner </a>
                <a onclick={handleClick} href="/pet">Pet</a>
              </div>
            </div>
            <div class="flex flex-col p-2 text-gray-600 gap-1">
              <span class="text-lg text-gray-900 capitalize">Clinic</span>
              <div class="flex flex-col p-4 text-gray-600 gap-1">
                <a onclick={handleClick} href="/visit">Visit </a>
                <a onclick={handleClick} href="/vet">Vet</a>
                <a onclick={handleClick} href="/enum/skill">Skill</a>
                <a onclick={handleClick} href="/enum/species">Species</a>
              </div>
            </div>
          </div>
        </nav>
      </aside>
    {/if}
    <Router>
      <Route path="/" component={AppHome} />
      <Route path="/home" component={AppHome} />
      <Route path="/help" component={AppHelp} />
      <Route path="/owner" component={Owner} />
      <Route path="/owner/:id" component={OwnerViewer} />
      <Route path="/pet" component={Pet} />
      <Route path="/pet/:id" component={PetViewer} />
      <Route path="/visit" component={Visit} />
      <Route path="/vet" component={Vet} />
      <Route path="/vet/:id" component={VetViewer} />
      <Route path="/enum/skill" component={Enum} art="skill" />
      <Route path="/enum/species" component={Enum} art="species" />
      <RouteNotFound>
        <h1>Ups!</h1>
      </RouteNotFound>
    </Router>
  </main>
  <footer class="flex justify-center bg-title-200 p-2 h-10">
    <nav class="flex flex-row text-sm text-title-600 gap-1">
      <a onclick={handleClick} href="/help">Impressum</a>
    </nav>
  </footer>
</article>
