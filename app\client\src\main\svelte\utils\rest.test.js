import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import * as restApi from "./rest";

describe("rest", () => {
  beforeEach(() => {
    vi.stubGlobal("location", {
      protocol: "http:",
      host: "localhost:5000",
      hostname: "localhost",
      port: "5000",
    });
  });

  afterEach(() => {
    vi.unstubAllGlobals();
    vi.resetAllMocks();
  });

  describe("apiExplorerUrl", () => {
    it("should return a valid URL", () => {
      const url = restApi.apiExplorerUrl();
      expect(url).toBe("http://localhost:8080/api/explorer");
    });
  });

  describe("apiGraphiqlUrl", () => {
    it("should return a valid URL", () => {
      const url = restApi.apiExplorerUrl();
      expect(url).toBe("http://localhost:8080/api/graphiql");
    });
  });
});
