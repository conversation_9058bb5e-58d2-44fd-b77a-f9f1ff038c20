plugins {
    id 'java'
    id 'io.spring.dependency-management'
    id 'org.springframework.boot'
    id 'com.diffplug.spotless'
}

spotless {
    encoding 'UTF-8'
    java {
        target fileTree(projectDir) {
            include 'src/main/java/**/*.java'
            exclude 'build/**'
        }
        leadingTabsToSpaces(4)
        removeUnusedImports()
    }
}

dependencies {
    implementation project(':lib:backend-data')
    // https://projectlombok.org
    implementation('org.projectlombok:lombok')
    annotationProcessor('org.projectlombok:lombok')
    // http://hsqldb.org/
    runtimeOnly('org.hsqldb:hsqldb')
}
dependencies {
    // https://spring.io/projects/spring-boot
    testImplementation('org.springframework.boot:spring-boot-starter-test')
    // https://junit.org/junit5
    testImplementation('org.junit.jupiter:junit-jupiter')
    testRuntimeOnly('org.junit.platform:junit-platform-launcher')
    // https://site.mockito.org/
    testImplementation('org.mockito:mockito-junit-jupiter')
    // https://playwright.dev/java/
    testImplementation(extraLibs.playwright)
}

processResources {
    from("${rootDir}")
    include 'VERSION'
}

test {
    filter {
        failOnNoMatchingTests false
    }
    reports {
        html.required = true
        html.destination = file("${rootDir}/pages/html/" + project.name + "/junit5")
    }
}

jar {
    enabled = false
}

bootJar {
    enabled = true
    manifest {
        attributes 'Specification-Title': project.name
        attributes 'Specification-Version': VERSION
        attributes 'Implementation-Title': project.name
        attributes 'Implementation-Version': project.version
    }
}

import java.util.Optional
ext.SERVER_NAME = Optional.ofNullable(System.getenv("SERVER_NAME"))
        .orElse(rootProject.name + "-" + project.name)
ext.SERVER_IMAGE = Optional.ofNullable(System.getenv("SERVER_IMAGE"))
            .orElse(rootProject.name + "/" + project.name)
ext.SERVER_TAG = Optional.ofNullable(System.getenv("SERVER_TAG"))
            .orElse('latest')

bootBuildImage {
    group = 'build'
    enabled = true
    imageName = SERVER_IMAGE + ":" + SERVER_TAG
    tags = [SERVER_IMAGE + ":" + VERSION]
    environment = [
        "BP_JVM_VERSION":"21.*",
        "BP_SPRING_CLOUD_BINDINGS_DISABLED":"true"
    ]
}

tasks.register('buildImage') {
    group = 'build'
    enabled = true
    mustRunAfter 'build'
    dependsOn 'bootBuildImage'
}

// https://docs.docker.com/reference/cli/docker/container/run/
tasks.register('imageRun') {
    group 'application'
    mustRunAfter 'buildImage'
    doLast {
        exec {
            workingDir "${projectDir}"
            executable 'docker'
            args 'run', '--name', SERVER_NAME, '--detach', '--rm', '-p', '8080:8080', SERVER_IMAGE
        }
    }
}

// https://docs.docker.com/reference/cli/docker/container/rm/
tasks.register('imageStop') {
    group 'application'
    doLast {
        exec {
            workingDir "${projectDir}"
            executable 'docker'
            args 'stop', SERVER_NAME
        }
    }
}

tasks.register('versionCheck', VersionCheckTask.class)  {
    group 'verification'
}

tasks.register('lint') {
    group = 'verification'
    dependsOn 'spotlessCheck'
}

tasks.register('format') {
    group = 'build'
    dependsOn 'spotlessApply'
}
