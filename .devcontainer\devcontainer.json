{"name": "petclinic-devcontainer", "image": "mcr.microsoft.com/devcontainers/base:ubuntu", "features": {"ghcr.io/devcontainers/features/java:1": {"version": "21", "jdkDistro": "tem", "installGradle": "true", "gradleVersion": "8.14"}, "ghcr.io/devcontainers/features/node:1": {"version": "22"}}, "customizations": {"vscode": {"extensions": ["vscjava.vscode-java-pack", "svelte.svelte-vscode", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-json", "redhat.vscode-yaml"]}}, "forwardPorts": [8080, 5000], "portsAttributes": {"8080": {"label": "Backend API", "onAutoForward": "notify"}, "5000": {"label": "Frontend", "onAutoForward": "openBrowser"}}, "postCreateCommand": "npm install --prefix app/client"}