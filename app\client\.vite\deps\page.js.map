{"version": 3, "sources": ["../../node_modules/page/page.mjs"], "sourcesContent": ["var isarray = Array.isArray || function (arr) {\n  return Object.prototype.toString.call(arr) == '[object Array]';\n};\n\n/**\n * Expose `pathToRegexp`.\n */\nvar pathToRegexp_1 = pathToRegexp;\nvar parse_1 = parse;\nvar compile_1 = compile;\nvar tokensToFunction_1 = tokensToFunction;\nvar tokensToRegExp_1 = tokensToRegExp;\n\n/**\n * The main path matching regexp utility.\n *\n * @type {RegExp}\n */\nvar PATH_REGEXP = new RegExp([\n  // Match escaped characters that would otherwise appear in future matches.\n  // This allows the user to escape special characters that won't transform.\n  '(\\\\\\\\.)',\n  // Match Express-style parameters and un-named parameters with a prefix\n  // and optional suffixes. Matches appear as:\n  //\n  // \"/:test(\\\\d+)?\" => [\"/\", \"test\", \"\\d+\", undefined, \"?\", undefined]\n  // \"/route(\\\\d+)\"  => [undefined, undefined, undefined, \"\\d+\", undefined, undefined]\n  // \"/*\"            => [\"/\", undefined, undefined, undefined, undefined, \"*\"]\n  '([\\\\/.])?(?:(?:\\\\:(\\\\w+)(?:\\\\(((?:\\\\\\\\.|[^()])+)\\\\))?|\\\\(((?:\\\\\\\\.|[^()])+)\\\\))([+*?])?|(\\\\*))'\n].join('|'), 'g');\n\n/**\n * Parse a string for the raw tokens.\n *\n * @param  {String} str\n * @return {Array}\n */\nfunction parse (str) {\n  var tokens = [];\n  var key = 0;\n  var index = 0;\n  var path = '';\n  var res;\n\n  while ((res = PATH_REGEXP.exec(str)) != null) {\n    var m = res[0];\n    var escaped = res[1];\n    var offset = res.index;\n    path += str.slice(index, offset);\n    index = offset + m.length;\n\n    // Ignore already escaped sequences.\n    if (escaped) {\n      path += escaped[1];\n      continue\n    }\n\n    // Push the current path onto the tokens.\n    if (path) {\n      tokens.push(path);\n      path = '';\n    }\n\n    var prefix = res[2];\n    var name = res[3];\n    var capture = res[4];\n    var group = res[5];\n    var suffix = res[6];\n    var asterisk = res[7];\n\n    var repeat = suffix === '+' || suffix === '*';\n    var optional = suffix === '?' || suffix === '*';\n    var delimiter = prefix || '/';\n    var pattern = capture || group || (asterisk ? '.*' : '[^' + delimiter + ']+?');\n\n    tokens.push({\n      name: name || key++,\n      prefix: prefix || '',\n      delimiter: delimiter,\n      optional: optional,\n      repeat: repeat,\n      pattern: escapeGroup(pattern)\n    });\n  }\n\n  // Match any characters still remaining.\n  if (index < str.length) {\n    path += str.substr(index);\n  }\n\n  // If the path exists, push it onto the end.\n  if (path) {\n    tokens.push(path);\n  }\n\n  return tokens\n}\n\n/**\n * Compile a string to a template function for the path.\n *\n * @param  {String}   str\n * @return {Function}\n */\nfunction compile (str) {\n  return tokensToFunction(parse(str))\n}\n\n/**\n * Expose a method for transforming tokens into the path function.\n */\nfunction tokensToFunction (tokens) {\n  // Compile all the tokens into regexps.\n  var matches = new Array(tokens.length);\n\n  // Compile all the patterns before compilation.\n  for (var i = 0; i < tokens.length; i++) {\n    if (typeof tokens[i] === 'object') {\n      matches[i] = new RegExp('^' + tokens[i].pattern + '$');\n    }\n  }\n\n  return function (obj) {\n    var path = '';\n    var data = obj || {};\n\n    for (var i = 0; i < tokens.length; i++) {\n      var token = tokens[i];\n\n      if (typeof token === 'string') {\n        path += token;\n\n        continue\n      }\n\n      var value = data[token.name];\n      var segment;\n\n      if (value == null) {\n        if (token.optional) {\n          continue\n        } else {\n          throw new TypeError('Expected \"' + token.name + '\" to be defined')\n        }\n      }\n\n      if (isarray(value)) {\n        if (!token.repeat) {\n          throw new TypeError('Expected \"' + token.name + '\" to not repeat, but received \"' + value + '\"')\n        }\n\n        if (value.length === 0) {\n          if (token.optional) {\n            continue\n          } else {\n            throw new TypeError('Expected \"' + token.name + '\" to not be empty')\n          }\n        }\n\n        for (var j = 0; j < value.length; j++) {\n          segment = encodeURIComponent(value[j]);\n\n          if (!matches[i].test(segment)) {\n            throw new TypeError('Expected all \"' + token.name + '\" to match \"' + token.pattern + '\", but received \"' + segment + '\"')\n          }\n\n          path += (j === 0 ? token.prefix : token.delimiter) + segment;\n        }\n\n        continue\n      }\n\n      segment = encodeURIComponent(value);\n\n      if (!matches[i].test(segment)) {\n        throw new TypeError('Expected \"' + token.name + '\" to match \"' + token.pattern + '\", but received \"' + segment + '\"')\n      }\n\n      path += token.prefix + segment;\n    }\n\n    return path\n  }\n}\n\n/**\n * Escape a regular expression string.\n *\n * @param  {String} str\n * @return {String}\n */\nfunction escapeString (str) {\n  return str.replace(/([.+*?=^!:${}()[\\]|\\/])/g, '\\\\$1')\n}\n\n/**\n * Escape the capturing group by escaping special characters and meaning.\n *\n * @param  {String} group\n * @return {String}\n */\nfunction escapeGroup (group) {\n  return group.replace(/([=!:$\\/()])/g, '\\\\$1')\n}\n\n/**\n * Attach the keys as a property of the regexp.\n *\n * @param  {RegExp} re\n * @param  {Array}  keys\n * @return {RegExp}\n */\nfunction attachKeys (re, keys) {\n  re.keys = keys;\n  return re\n}\n\n/**\n * Get the flags for a regexp from the options.\n *\n * @param  {Object} options\n * @return {String}\n */\nfunction flags (options) {\n  return options.sensitive ? '' : 'i'\n}\n\n/**\n * Pull out keys from a regexp.\n *\n * @param  {RegExp} path\n * @param  {Array}  keys\n * @return {RegExp}\n */\nfunction regexpToRegexp (path, keys) {\n  // Use a negative lookahead to match only capturing groups.\n  var groups = path.source.match(/\\((?!\\?)/g);\n\n  if (groups) {\n    for (var i = 0; i < groups.length; i++) {\n      keys.push({\n        name: i,\n        prefix: null,\n        delimiter: null,\n        optional: false,\n        repeat: false,\n        pattern: null\n      });\n    }\n  }\n\n  return attachKeys(path, keys)\n}\n\n/**\n * Transform an array into a regexp.\n *\n * @param  {Array}  path\n * @param  {Array}  keys\n * @param  {Object} options\n * @return {RegExp}\n */\nfunction arrayToRegexp (path, keys, options) {\n  var parts = [];\n\n  for (var i = 0; i < path.length; i++) {\n    parts.push(pathToRegexp(path[i], keys, options).source);\n  }\n\n  var regexp = new RegExp('(?:' + parts.join('|') + ')', flags(options));\n\n  return attachKeys(regexp, keys)\n}\n\n/**\n * Create a path regexp from string input.\n *\n * @param  {String} path\n * @param  {Array}  keys\n * @param  {Object} options\n * @return {RegExp}\n */\nfunction stringToRegexp (path, keys, options) {\n  var tokens = parse(path);\n  var re = tokensToRegExp(tokens, options);\n\n  // Attach keys back to the regexp.\n  for (var i = 0; i < tokens.length; i++) {\n    if (typeof tokens[i] !== 'string') {\n      keys.push(tokens[i]);\n    }\n  }\n\n  return attachKeys(re, keys)\n}\n\n/**\n * Expose a function for taking tokens and returning a RegExp.\n *\n * @param  {Array}  tokens\n * @param  {Array}  keys\n * @param  {Object} options\n * @return {RegExp}\n */\nfunction tokensToRegExp (tokens, options) {\n  options = options || {};\n\n  var strict = options.strict;\n  var end = options.end !== false;\n  var route = '';\n  var lastToken = tokens[tokens.length - 1];\n  var endsWithSlash = typeof lastToken === 'string' && /\\/$/.test(lastToken);\n\n  // Iterate over the tokens and create our regexp string.\n  for (var i = 0; i < tokens.length; i++) {\n    var token = tokens[i];\n\n    if (typeof token === 'string') {\n      route += escapeString(token);\n    } else {\n      var prefix = escapeString(token.prefix);\n      var capture = token.pattern;\n\n      if (token.repeat) {\n        capture += '(?:' + prefix + capture + ')*';\n      }\n\n      if (token.optional) {\n        if (prefix) {\n          capture = '(?:' + prefix + '(' + capture + '))?';\n        } else {\n          capture = '(' + capture + ')?';\n        }\n      } else {\n        capture = prefix + '(' + capture + ')';\n      }\n\n      route += capture;\n    }\n  }\n\n  // In non-strict mode we allow a slash at the end of match. If the path to\n  // match already ends with a slash, we remove it for consistency. The slash\n  // is valid at the end of a path match, not in the middle. This is important\n  // in non-ending mode, where \"/test/\" shouldn't match \"/test//route\".\n  if (!strict) {\n    route = (endsWithSlash ? route.slice(0, -2) : route) + '(?:\\\\/(?=$))?';\n  }\n\n  if (end) {\n    route += '$';\n  } else {\n    // In non-ending mode, we need the capturing groups to match as much as\n    // possible by using a positive lookahead to the end or next path segment.\n    route += strict && endsWithSlash ? '' : '(?=\\\\/|$)';\n  }\n\n  return new RegExp('^' + route, flags(options))\n}\n\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n *\n * @param  {(String|RegExp|Array)} path\n * @param  {Array}                 [keys]\n * @param  {Object}                [options]\n * @return {RegExp}\n */\nfunction pathToRegexp (path, keys, options) {\n  keys = keys || [];\n\n  if (!isarray(keys)) {\n    options = keys;\n    keys = [];\n  } else if (!options) {\n    options = {};\n  }\n\n  if (path instanceof RegExp) {\n    return regexpToRegexp(path, keys, options)\n  }\n\n  if (isarray(path)) {\n    return arrayToRegexp(path, keys, options)\n  }\n\n  return stringToRegexp(path, keys, options)\n}\n\npathToRegexp_1.parse = parse_1;\npathToRegexp_1.compile = compile_1;\npathToRegexp_1.tokensToFunction = tokensToFunction_1;\npathToRegexp_1.tokensToRegExp = tokensToRegExp_1;\n\n/**\n   * Module dependencies.\n   */\n\n  \n\n  /**\n   * Short-cuts for global-object checks\n   */\n\n  var hasDocument = ('undefined' !== typeof document);\n  var hasWindow = ('undefined' !== typeof window);\n  var hasHistory = ('undefined' !== typeof history);\n  var hasProcess = typeof process !== 'undefined';\n\n  /**\n   * Detect click event\n   */\n  var clickEvent = hasDocument && document.ontouchstart ? 'touchstart' : 'click';\n\n  /**\n   * To work properly with the URL\n   * history.location generated polyfill in https://github.com/devote/HTML5-History-API\n   */\n\n  var isLocation = hasWindow && !!(window.history.location || window.location);\n\n  /**\n   * The page instance\n   * @api private\n   */\n  function Page() {\n    // public things\n    this.callbacks = [];\n    this.exits = [];\n    this.current = '';\n    this.len = 0;\n\n    // private things\n    this._decodeURLComponents = true;\n    this._base = '';\n    this._strict = false;\n    this._running = false;\n    this._hashbang = false;\n\n    // bound functions\n    this.clickHandler = this.clickHandler.bind(this);\n    this._onpopstate = this._onpopstate.bind(this);\n  }\n\n  /**\n   * Configure the instance of page. This can be called multiple times.\n   *\n   * @param {Object} options\n   * @api public\n   */\n\n  Page.prototype.configure = function(options) {\n    var opts = options || {};\n\n    this._window = opts.window || (hasWindow && window);\n    this._decodeURLComponents = opts.decodeURLComponents !== false;\n    this._popstate = opts.popstate !== false && hasWindow;\n    this._click = opts.click !== false && hasDocument;\n    this._hashbang = !!opts.hashbang;\n\n    var _window = this._window;\n    if(this._popstate) {\n      _window.addEventListener('popstate', this._onpopstate, false);\n    } else if(hasWindow) {\n      _window.removeEventListener('popstate', this._onpopstate, false);\n    }\n\n    if (this._click) {\n      _window.document.addEventListener(clickEvent, this.clickHandler, false);\n    } else if(hasDocument) {\n      _window.document.removeEventListener(clickEvent, this.clickHandler, false);\n    }\n\n    if(this._hashbang && hasWindow && !hasHistory) {\n      _window.addEventListener('hashchange', this._onpopstate, false);\n    } else if(hasWindow) {\n      _window.removeEventListener('hashchange', this._onpopstate, false);\n    }\n  };\n\n  /**\n   * Get or set basepath to `path`.\n   *\n   * @param {string} path\n   * @api public\n   */\n\n  Page.prototype.base = function(path) {\n    if (0 === arguments.length) return this._base;\n    this._base = path;\n  };\n\n  /**\n   * Gets the `base`, which depends on whether we are using History or\n   * hashbang routing.\n\n   * @api private\n   */\n  Page.prototype._getBase = function() {\n    var base = this._base;\n    if(!!base) return base;\n    var loc = hasWindow && this._window && this._window.location;\n\n    if(hasWindow && this._hashbang && loc && loc.protocol === 'file:') {\n      base = loc.pathname;\n    }\n\n    return base;\n  };\n\n  /**\n   * Get or set strict path matching to `enable`\n   *\n   * @param {boolean} enable\n   * @api public\n   */\n\n  Page.prototype.strict = function(enable) {\n    if (0 === arguments.length) return this._strict;\n    this._strict = enable;\n  };\n\n\n  /**\n   * Bind with the given `options`.\n   *\n   * Options:\n   *\n   *    - `click` bind to click events [true]\n   *    - `popstate` bind to popstate [true]\n   *    - `dispatch` perform initial dispatch [true]\n   *\n   * @param {Object} options\n   * @api public\n   */\n\n  Page.prototype.start = function(options) {\n    var opts = options || {};\n    this.configure(opts);\n\n    if (false === opts.dispatch) return;\n    this._running = true;\n\n    var url;\n    if(isLocation) {\n      var window = this._window;\n      var loc = window.location;\n\n      if(this._hashbang && ~loc.hash.indexOf('#!')) {\n        url = loc.hash.substr(2) + loc.search;\n      } else if (this._hashbang) {\n        url = loc.search + loc.hash;\n      } else {\n        url = loc.pathname + loc.search + loc.hash;\n      }\n    }\n\n    this.replace(url, null, true, opts.dispatch);\n  };\n\n  /**\n   * Unbind click and popstate event handlers.\n   *\n   * @api public\n   */\n\n  Page.prototype.stop = function() {\n    if (!this._running) return;\n    this.current = '';\n    this.len = 0;\n    this._running = false;\n\n    var window = this._window;\n    this._click && window.document.removeEventListener(clickEvent, this.clickHandler, false);\n    hasWindow && window.removeEventListener('popstate', this._onpopstate, false);\n    hasWindow && window.removeEventListener('hashchange', this._onpopstate, false);\n  };\n\n  /**\n   * Show `path` with optional `state` object.\n   *\n   * @param {string} path\n   * @param {Object=} state\n   * @param {boolean=} dispatch\n   * @param {boolean=} push\n   * @return {!Context}\n   * @api public\n   */\n\n  Page.prototype.show = function(path, state, dispatch, push) {\n    var ctx = new Context(path, state, this),\n      prev = this.prevContext;\n    this.prevContext = ctx;\n    this.current = ctx.path;\n    if (false !== dispatch) this.dispatch(ctx, prev);\n    if (false !== ctx.handled && false !== push) ctx.pushState();\n    return ctx;\n  };\n\n  /**\n   * Goes back in the history\n   * Back should always let the current route push state and then go back.\n   *\n   * @param {string} path - fallback path to go back if no more history exists, if undefined defaults to page.base\n   * @param {Object=} state\n   * @api public\n   */\n\n  Page.prototype.back = function(path, state) {\n    var page = this;\n    if (this.len > 0) {\n      var window = this._window;\n      // this may need more testing to see if all browsers\n      // wait for the next tick to go back in history\n      hasHistory && window.history.back();\n      this.len--;\n    } else if (path) {\n      setTimeout(function() {\n        page.show(path, state);\n      });\n    } else {\n      setTimeout(function() {\n        page.show(page._getBase(), state);\n      });\n    }\n  };\n\n  /**\n   * Register route to redirect from one path to other\n   * or just redirect to another route\n   *\n   * @param {string} from - if param 'to' is undefined redirects to 'from'\n   * @param {string=} to\n   * @api public\n   */\n  Page.prototype.redirect = function(from, to) {\n    var inst = this;\n\n    // Define route from a path to another\n    if ('string' === typeof from && 'string' === typeof to) {\n      page.call(this, from, function(e) {\n        setTimeout(function() {\n          inst.replace(/** @type {!string} */ (to));\n        }, 0);\n      });\n    }\n\n    // Wait for the push state and replace it with another\n    if ('string' === typeof from && 'undefined' === typeof to) {\n      setTimeout(function() {\n        inst.replace(from);\n      }, 0);\n    }\n  };\n\n  /**\n   * Replace `path` with optional `state` object.\n   *\n   * @param {string} path\n   * @param {Object=} state\n   * @param {boolean=} init\n   * @param {boolean=} dispatch\n   * @return {!Context}\n   * @api public\n   */\n\n\n  Page.prototype.replace = function(path, state, init, dispatch) {\n    var ctx = new Context(path, state, this),\n      prev = this.prevContext;\n    this.prevContext = ctx;\n    this.current = ctx.path;\n    ctx.init = init;\n    ctx.save(); // save before dispatching, which may redirect\n    if (false !== dispatch) this.dispatch(ctx, prev);\n    return ctx;\n  };\n\n  /**\n   * Dispatch the given `ctx`.\n   *\n   * @param {Context} ctx\n   * @api private\n   */\n\n  Page.prototype.dispatch = function(ctx, prev) {\n    var i = 0, j = 0, page = this;\n\n    function nextExit() {\n      var fn = page.exits[j++];\n      if (!fn) return nextEnter();\n      fn(prev, nextExit);\n    }\n\n    function nextEnter() {\n      var fn = page.callbacks[i++];\n\n      if (ctx.path !== page.current) {\n        ctx.handled = false;\n        return;\n      }\n      if (!fn) return unhandled.call(page, ctx);\n      fn(ctx, nextEnter);\n    }\n\n    if (prev) {\n      nextExit();\n    } else {\n      nextEnter();\n    }\n  };\n\n  /**\n   * Register an exit route on `path` with\n   * callback `fn()`, which will be called\n   * on the previous context when a new\n   * page is visited.\n   */\n  Page.prototype.exit = function(path, fn) {\n    if (typeof path === 'function') {\n      return this.exit('*', path);\n    }\n\n    var route = new Route(path, null, this);\n    for (var i = 1; i < arguments.length; ++i) {\n      this.exits.push(route.middleware(arguments[i]));\n    }\n  };\n\n  /**\n   * Handle \"click\" events.\n   */\n\n  /* jshint +W054 */\n  Page.prototype.clickHandler = function(e) {\n    if (1 !== this._which(e)) return;\n\n    if (e.metaKey || e.ctrlKey || e.shiftKey) return;\n    if (e.defaultPrevented) return;\n\n    // ensure link\n    // use shadow dom when available if not, fall back to composedPath()\n    // for browsers that only have shady\n    var el = e.target;\n    var eventPath = e.path || (e.composedPath ? e.composedPath() : null);\n\n    if(eventPath) {\n      for (var i = 0; i < eventPath.length; i++) {\n        if (!eventPath[i].nodeName) continue;\n        if (eventPath[i].nodeName.toUpperCase() !== 'A') continue;\n        if (!eventPath[i].href) continue;\n\n        el = eventPath[i];\n        break;\n      }\n    }\n\n    // continue ensure link\n    // el.nodeName for svg links are 'a' instead of 'A'\n    while (el && 'A' !== el.nodeName.toUpperCase()) el = el.parentNode;\n    if (!el || 'A' !== el.nodeName.toUpperCase()) return;\n\n    // check if link is inside an svg\n    // in this case, both href and target are always inside an object\n    var svg = (typeof el.href === 'object') && el.href.constructor.name === 'SVGAnimatedString';\n\n    // Ignore if tag has\n    // 1. \"download\" attribute\n    // 2. rel=\"external\" attribute\n    if (el.hasAttribute('download') || el.getAttribute('rel') === 'external') return;\n\n    // ensure non-hash for the same path\n    var link = el.getAttribute('href');\n    if(!this._hashbang && this._samePath(el) && (el.hash || '#' === link)) return;\n\n    // Check for mailto: in the href\n    if (link && link.indexOf('mailto:') > -1) return;\n\n    // check target\n    // svg target is an object and its desired value is in .baseVal property\n    if (svg ? el.target.baseVal : el.target) return;\n\n    // x-origin\n    // note: svg links that are not relative don't call click events (and skip page.js)\n    // consequently, all svg links tested inside page.js are relative and in the same origin\n    if (!svg && !this.sameOrigin(el.href)) return;\n\n    // rebuild path\n    // There aren't .pathname and .search properties in svg links, so we use href\n    // Also, svg href is an object and its desired value is in .baseVal property\n    var path = svg ? el.href.baseVal : (el.pathname + el.search + (el.hash || ''));\n\n    path = path[0] !== '/' ? '/' + path : path;\n\n    // strip leading \"/[drive letter]:\" on NW.js on Windows\n    if (hasProcess && path.match(/^\\/[a-zA-Z]:\\//)) {\n      path = path.replace(/^\\/[a-zA-Z]:\\//, '/');\n    }\n\n    // same page\n    var orig = path;\n    var pageBase = this._getBase();\n\n    if (path.indexOf(pageBase) === 0) {\n      path = path.substr(pageBase.length);\n    }\n\n    if (this._hashbang) path = path.replace('#!', '');\n\n    if (pageBase && orig === path && (!isLocation || this._window.location.protocol !== 'file:')) {\n      return;\n    }\n\n    e.preventDefault();\n    this.show(orig);\n  };\n\n  /**\n   * Handle \"populate\" events.\n   * @api private\n   */\n\n  Page.prototype._onpopstate = (function () {\n    var loaded = false;\n    if ( ! hasWindow ) {\n      return function () {};\n    }\n    if (hasDocument && document.readyState === 'complete') {\n      loaded = true;\n    } else {\n      window.addEventListener('load', function() {\n        setTimeout(function() {\n          loaded = true;\n        }, 0);\n      });\n    }\n    return function onpopstate(e) {\n      if (!loaded) return;\n      var page = this;\n      if (e.state) {\n        var path = e.state.path;\n        page.replace(path, e.state);\n      } else if (isLocation) {\n        var loc = page._window.location;\n        page.show(loc.pathname + loc.search + loc.hash, undefined, undefined, false);\n      }\n    };\n  })();\n\n  /**\n   * Event button.\n   */\n  Page.prototype._which = function(e) {\n    e = e || (hasWindow && this._window.event);\n    return null == e.which ? e.button : e.which;\n  };\n\n  /**\n   * Convert to a URL object\n   * @api private\n   */\n  Page.prototype._toURL = function(href) {\n    var window = this._window;\n    if(typeof URL === 'function' && isLocation) {\n      return new URL(href, window.location.toString());\n    } else if (hasDocument) {\n      var anc = window.document.createElement('a');\n      anc.href = href;\n      return anc;\n    }\n  };\n\n  /**\n   * Check if `href` is the same origin.\n   * @param {string} href\n   * @api public\n   */\n  Page.prototype.sameOrigin = function(href) {\n    if(!href || !isLocation) return false;\n\n    var url = this._toURL(href);\n    var window = this._window;\n\n    var loc = window.location;\n\n    /*\n       When the port is the default http port 80 for http, or 443 for\n       https, internet explorer 11 returns an empty string for loc.port,\n       so we need to compare loc.port with an empty string if url.port\n       is the default port 80 or 443.\n       Also the comparition with `port` is changed from `===` to `==` because\n       `port` can be a string sometimes. This only applies to ie11.\n    */\n    return loc.protocol === url.protocol &&\n      loc.hostname === url.hostname &&\n      (loc.port === url.port || loc.port === '' && (url.port == 80 || url.port == 443)); // jshint ignore:line\n  };\n\n  /**\n   * @api private\n   */\n  Page.prototype._samePath = function(url) {\n    if(!isLocation) return false;\n    var window = this._window;\n    var loc = window.location;\n    return url.pathname === loc.pathname &&\n      url.search === loc.search;\n  };\n\n  /**\n   * Remove URL encoding from the given `str`.\n   * Accommodates whitespace in both x-www-form-urlencoded\n   * and regular percent-encoded form.\n   *\n   * @param {string} val - URL component to decode\n   * @api private\n   */\n  Page.prototype._decodeURLEncodedURIComponent = function(val) {\n    if (typeof val !== 'string') { return val; }\n    return this._decodeURLComponents ? decodeURIComponent(val.replace(/\\+/g, ' ')) : val;\n  };\n\n  /**\n   * Create a new `page` instance and function\n   */\n  function createPage() {\n    var pageInstance = new Page();\n\n    function pageFn(/* args */) {\n      return page.apply(pageInstance, arguments);\n    }\n\n    // Copy all of the things over. In 2.0 maybe we use setPrototypeOf\n    pageFn.callbacks = pageInstance.callbacks;\n    pageFn.exits = pageInstance.exits;\n    pageFn.base = pageInstance.base.bind(pageInstance);\n    pageFn.strict = pageInstance.strict.bind(pageInstance);\n    pageFn.start = pageInstance.start.bind(pageInstance);\n    pageFn.stop = pageInstance.stop.bind(pageInstance);\n    pageFn.show = pageInstance.show.bind(pageInstance);\n    pageFn.back = pageInstance.back.bind(pageInstance);\n    pageFn.redirect = pageInstance.redirect.bind(pageInstance);\n    pageFn.replace = pageInstance.replace.bind(pageInstance);\n    pageFn.dispatch = pageInstance.dispatch.bind(pageInstance);\n    pageFn.exit = pageInstance.exit.bind(pageInstance);\n    pageFn.configure = pageInstance.configure.bind(pageInstance);\n    pageFn.sameOrigin = pageInstance.sameOrigin.bind(pageInstance);\n    pageFn.clickHandler = pageInstance.clickHandler.bind(pageInstance);\n\n    pageFn.create = createPage;\n\n    Object.defineProperty(pageFn, 'len', {\n      get: function(){\n        return pageInstance.len;\n      },\n      set: function(val) {\n        pageInstance.len = val;\n      }\n    });\n\n    Object.defineProperty(pageFn, 'current', {\n      get: function(){\n        return pageInstance.current;\n      },\n      set: function(val) {\n        pageInstance.current = val;\n      }\n    });\n\n    // In 2.0 these can be named exports\n    pageFn.Context = Context;\n    pageFn.Route = Route;\n\n    return pageFn;\n  }\n\n  /**\n   * Register `path` with callback `fn()`,\n   * or route `path`, or redirection,\n   * or `page.start()`.\n   *\n   *   page(fn);\n   *   page('*', fn);\n   *   page('/user/:id', load, user);\n   *   page('/user/' + user.id, { some: 'thing' });\n   *   page('/user/' + user.id);\n   *   page('/from', '/to')\n   *   page();\n   *\n   * @param {string|!Function|!Object} path\n   * @param {Function=} fn\n   * @api public\n   */\n\n  function page(path, fn) {\n    // <callback>\n    if ('function' === typeof path) {\n      return page.call(this, '*', path);\n    }\n\n    // route <path> to <callback ...>\n    if ('function' === typeof fn) {\n      var route = new Route(/** @type {string} */ (path), null, this);\n      for (var i = 1; i < arguments.length; ++i) {\n        this.callbacks.push(route.middleware(arguments[i]));\n      }\n      // show <path> with [state]\n    } else if ('string' === typeof path) {\n      this['string' === typeof fn ? 'redirect' : 'show'](path, fn);\n      // start [options]\n    } else {\n      this.start(path);\n    }\n  }\n\n  /**\n   * Unhandled `ctx`. When it's not the initial\n   * popstate then redirect. If you wish to handle\n   * 404s on your own use `page('*', callback)`.\n   *\n   * @param {Context} ctx\n   * @api private\n   */\n  function unhandled(ctx) {\n    if (ctx.handled) return;\n    var current;\n    var page = this;\n    var window = page._window;\n\n    if (page._hashbang) {\n      current = isLocation && this._getBase() + window.location.hash.replace('#!', '');\n    } else {\n      current = isLocation && window.location.pathname + window.location.search;\n    }\n\n    if (current === ctx.canonicalPath) return;\n    page.stop();\n    ctx.handled = false;\n    isLocation && (window.location.href = ctx.canonicalPath);\n  }\n\n  /**\n   * Escapes RegExp characters in the given string.\n   *\n   * @param {string} s\n   * @api private\n   */\n  function escapeRegExp(s) {\n    return s.replace(/([.+*?=^!:${}()[\\]|/\\\\])/g, '\\\\$1');\n  }\n\n  /**\n   * Initialize a new \"request\" `Context`\n   * with the given `path` and optional initial `state`.\n   *\n   * @constructor\n   * @param {string} path\n   * @param {Object=} state\n   * @api public\n   */\n\n  function Context(path, state, pageInstance) {\n    var _page = this.page = pageInstance || page;\n    var window = _page._window;\n    var hashbang = _page._hashbang;\n\n    var pageBase = _page._getBase();\n    if ('/' === path[0] && 0 !== path.indexOf(pageBase)) path = pageBase + (hashbang ? '#!' : '') + path;\n    var i = path.indexOf('?');\n\n    this.canonicalPath = path;\n    var re = new RegExp('^' + escapeRegExp(pageBase));\n    this.path = path.replace(re, '') || '/';\n    if (hashbang) this.path = this.path.replace('#!', '') || '/';\n\n    this.title = (hasDocument && window.document.title);\n    this.state = state || {};\n    this.state.path = path;\n    this.querystring = ~i ? _page._decodeURLEncodedURIComponent(path.slice(i + 1)) : '';\n    this.pathname = _page._decodeURLEncodedURIComponent(~i ? path.slice(0, i) : path);\n    this.params = {};\n\n    // fragment\n    this.hash = '';\n    if (!hashbang) {\n      if (!~this.path.indexOf('#')) return;\n      var parts = this.path.split('#');\n      this.path = this.pathname = parts[0];\n      this.hash = _page._decodeURLEncodedURIComponent(parts[1]) || '';\n      this.querystring = this.querystring.split('#')[0];\n    }\n  }\n\n  /**\n   * Push state.\n   *\n   * @api private\n   */\n\n  Context.prototype.pushState = function() {\n    var page = this.page;\n    var window = page._window;\n    var hashbang = page._hashbang;\n\n    page.len++;\n    if (hasHistory) {\n        window.history.pushState(this.state, this.title,\n          hashbang && this.path !== '/' ? '#!' + this.path : this.canonicalPath);\n    }\n  };\n\n  /**\n   * Save the context state.\n   *\n   * @api public\n   */\n\n  Context.prototype.save = function() {\n    var page = this.page;\n    if (hasHistory) {\n        page._window.history.replaceState(this.state, this.title,\n          page._hashbang && this.path !== '/' ? '#!' + this.path : this.canonicalPath);\n    }\n  };\n\n  /**\n   * Initialize `Route` with the given HTTP `path`,\n   * and an array of `callbacks` and `options`.\n   *\n   * Options:\n   *\n   *   - `sensitive`    enable case-sensitive routes\n   *   - `strict`       enable strict matching for trailing slashes\n   *\n   * @constructor\n   * @param {string} path\n   * @param {Object=} options\n   * @api private\n   */\n\n  function Route(path, options, page) {\n    var _page = this.page = page || globalPage;\n    var opts = options || {};\n    opts.strict = opts.strict || _page._strict;\n    this.path = (path === '*') ? '(.*)' : path;\n    this.method = 'GET';\n    this.regexp = pathToRegexp_1(this.path, this.keys = [], opts);\n  }\n\n  /**\n   * Return route middleware with\n   * the given callback `fn()`.\n   *\n   * @param {Function} fn\n   * @return {Function}\n   * @api public\n   */\n\n  Route.prototype.middleware = function(fn) {\n    var self = this;\n    return function(ctx, next) {\n      if (self.match(ctx.path, ctx.params)) {\n        ctx.routePath = self.path;\n        return fn(ctx, next);\n      }\n      next();\n    };\n  };\n\n  /**\n   * Check if this route matches `path`, if so\n   * populate `params`.\n   *\n   * @param {string} path\n   * @param {Object} params\n   * @return {boolean}\n   * @api private\n   */\n\n  Route.prototype.match = function(path, params) {\n    var keys = this.keys,\n      qsIndex = path.indexOf('?'),\n      pathname = ~qsIndex ? path.slice(0, qsIndex) : path,\n      m = this.regexp.exec(decodeURIComponent(pathname));\n\n    if (!m) return false;\n\n    delete params[0];\n\n    for (var i = 1, len = m.length; i < len; ++i) {\n      var key = keys[i - 1];\n      var val = this.page._decodeURLEncodedURIComponent(m[i]);\n      if (val !== undefined || !(hasOwnProperty.call(params, key.name))) {\n        params[key.name] = val;\n      }\n    }\n\n    return true;\n  };\n\n\n  /**\n   * Module exports.\n   */\n\n  var globalPage = createPage();\n  var page_js = globalPage;\n  var default_1 = globalPage;\n\npage_js.default = default_1;\n\nexport default page_js;\n"], "mappings": ";AAAA,IAAI,UAAU,MAAM,WAAW,SAAU,KAAK;AAC5C,SAAO,OAAO,UAAU,SAAS,KAAK,GAAG,KAAK;AAChD;AAKA,IAAI,iBAAiB;AACrB,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,qBAAqB;AACzB,IAAI,mBAAmB;AAOvB,IAAI,cAAc,IAAI,OAAO;AAAA;AAAA;AAAA,EAG3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA;AACF,EAAE,KAAK,GAAG,GAAG,GAAG;AAQhB,SAAS,MAAO,KAAK;AACnB,MAAI,SAAS,CAAC;AACd,MAAI,MAAM;AACV,MAAI,QAAQ;AACZ,MAAI,OAAO;AACX,MAAI;AAEJ,UAAQ,MAAM,YAAY,KAAK,GAAG,MAAM,MAAM;AAC5C,QAAI,IAAI,IAAI,CAAC;AACb,QAAI,UAAU,IAAI,CAAC;AACnB,QAAI,SAAS,IAAI;AACjB,YAAQ,IAAI,MAAM,OAAO,MAAM;AAC/B,YAAQ,SAAS,EAAE;AAGnB,QAAI,SAAS;AACX,cAAQ,QAAQ,CAAC;AACjB;AAAA,IACF;AAGA,QAAI,MAAM;AACR,aAAO,KAAK,IAAI;AAChB,aAAO;AAAA,IACT;AAEA,QAAI,SAAS,IAAI,CAAC;AAClB,QAAI,OAAO,IAAI,CAAC;AAChB,QAAI,UAAU,IAAI,CAAC;AACnB,QAAI,QAAQ,IAAI,CAAC;AACjB,QAAI,SAAS,IAAI,CAAC;AAClB,QAAI,WAAW,IAAI,CAAC;AAEpB,QAAI,SAAS,WAAW,OAAO,WAAW;AAC1C,QAAI,WAAW,WAAW,OAAO,WAAW;AAC5C,QAAI,YAAY,UAAU;AAC1B,QAAI,UAAU,WAAW,UAAU,WAAW,OAAO,OAAO,YAAY;AAExE,WAAO,KAAK;AAAA,MACV,MAAM,QAAQ;AAAA,MACd,QAAQ,UAAU;AAAA,MAClB;AAAA,MACA;AAAA,MACA;AAAA,MACA,SAAS,YAAY,OAAO;AAAA,IAC9B,CAAC;AAAA,EACH;AAGA,MAAI,QAAQ,IAAI,QAAQ;AACtB,YAAQ,IAAI,OAAO,KAAK;AAAA,EAC1B;AAGA,MAAI,MAAM;AACR,WAAO,KAAK,IAAI;AAAA,EAClB;AAEA,SAAO;AACT;AAQA,SAAS,QAAS,KAAK;AACrB,SAAO,iBAAiB,MAAM,GAAG,CAAC;AACpC;AAKA,SAAS,iBAAkB,QAAQ;AAEjC,MAAI,UAAU,IAAI,MAAM,OAAO,MAAM;AAGrC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AACjC,cAAQ,CAAC,IAAI,IAAI,OAAO,MAAM,OAAO,CAAC,EAAE,UAAU,GAAG;AAAA,IACvD;AAAA,EACF;AAEA,SAAO,SAAU,KAAK;AACpB,QAAI,OAAO;AACX,QAAI,OAAO,OAAO,CAAC;AAEnB,aAASA,KAAI,GAAGA,KAAI,OAAO,QAAQA,MAAK;AACtC,UAAI,QAAQ,OAAOA,EAAC;AAEpB,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ;AAER;AAAA,MACF;AAEA,UAAI,QAAQ,KAAK,MAAM,IAAI;AAC3B,UAAI;AAEJ,UAAI,SAAS,MAAM;AACjB,YAAI,MAAM,UAAU;AAClB;AAAA,QACF,OAAO;AACL,gBAAM,IAAI,UAAU,eAAe,MAAM,OAAO,iBAAiB;AAAA,QACnE;AAAA,MACF;AAEA,UAAI,QAAQ,KAAK,GAAG;AAClB,YAAI,CAAC,MAAM,QAAQ;AACjB,gBAAM,IAAI,UAAU,eAAe,MAAM,OAAO,oCAAoC,QAAQ,GAAG;AAAA,QACjG;AAEA,YAAI,MAAM,WAAW,GAAG;AACtB,cAAI,MAAM,UAAU;AAClB;AAAA,UACF,OAAO;AACL,kBAAM,IAAI,UAAU,eAAe,MAAM,OAAO,mBAAmB;AAAA,UACrE;AAAA,QACF;AAEA,iBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,oBAAU,mBAAmB,MAAM,CAAC,CAAC;AAErC,cAAI,CAAC,QAAQA,EAAC,EAAE,KAAK,OAAO,GAAG;AAC7B,kBAAM,IAAI,UAAU,mBAAmB,MAAM,OAAO,iBAAiB,MAAM,UAAU,sBAAsB,UAAU,GAAG;AAAA,UAC1H;AAEA,mBAAS,MAAM,IAAI,MAAM,SAAS,MAAM,aAAa;AAAA,QACvD;AAEA;AAAA,MACF;AAEA,gBAAU,mBAAmB,KAAK;AAElC,UAAI,CAAC,QAAQA,EAAC,EAAE,KAAK,OAAO,GAAG;AAC7B,cAAM,IAAI,UAAU,eAAe,MAAM,OAAO,iBAAiB,MAAM,UAAU,sBAAsB,UAAU,GAAG;AAAA,MACtH;AAEA,cAAQ,MAAM,SAAS;AAAA,IACzB;AAEA,WAAO;AAAA,EACT;AACF;AAQA,SAAS,aAAc,KAAK;AAC1B,SAAO,IAAI,QAAQ,4BAA4B,MAAM;AACvD;AAQA,SAAS,YAAa,OAAO;AAC3B,SAAO,MAAM,QAAQ,iBAAiB,MAAM;AAC9C;AASA,SAAS,WAAY,IAAI,MAAM;AAC7B,KAAG,OAAO;AACV,SAAO;AACT;AAQA,SAAS,MAAO,SAAS;AACvB,SAAO,QAAQ,YAAY,KAAK;AAClC;AASA,SAAS,eAAgB,MAAM,MAAM;AAEnC,MAAI,SAAS,KAAK,OAAO,MAAM,WAAW;AAE1C,MAAI,QAAQ;AACV,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAK,KAAK;AAAA,QACR,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,UAAU;AAAA,QACV,QAAQ;AAAA,QACR,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA,EACF;AAEA,SAAO,WAAW,MAAM,IAAI;AAC9B;AAUA,SAAS,cAAe,MAAM,MAAM,SAAS;AAC3C,MAAI,QAAQ,CAAC;AAEb,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAM,KAAK,aAAa,KAAK,CAAC,GAAG,MAAM,OAAO,EAAE,MAAM;AAAA,EACxD;AAEA,MAAI,SAAS,IAAI,OAAO,QAAQ,MAAM,KAAK,GAAG,IAAI,KAAK,MAAM,OAAO,CAAC;AAErE,SAAO,WAAW,QAAQ,IAAI;AAChC;AAUA,SAAS,eAAgB,MAAM,MAAM,SAAS;AAC5C,MAAI,SAAS,MAAM,IAAI;AACvB,MAAI,KAAK,eAAe,QAAQ,OAAO;AAGvC,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,OAAO,OAAO,CAAC,MAAM,UAAU;AACjC,WAAK,KAAK,OAAO,CAAC,CAAC;AAAA,IACrB;AAAA,EACF;AAEA,SAAO,WAAW,IAAI,IAAI;AAC5B;AAUA,SAAS,eAAgB,QAAQ,SAAS;AACxC,YAAU,WAAW,CAAC;AAEtB,MAAI,SAAS,QAAQ;AACrB,MAAI,MAAM,QAAQ,QAAQ;AAC1B,MAAI,QAAQ;AACZ,MAAI,YAAY,OAAO,OAAO,SAAS,CAAC;AACxC,MAAI,gBAAgB,OAAO,cAAc,YAAY,MAAM,KAAK,SAAS;AAGzE,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,QAAI,QAAQ,OAAO,CAAC;AAEpB,QAAI,OAAO,UAAU,UAAU;AAC7B,eAAS,aAAa,KAAK;AAAA,IAC7B,OAAO;AACL,UAAI,SAAS,aAAa,MAAM,MAAM;AACtC,UAAI,UAAU,MAAM;AAEpB,UAAI,MAAM,QAAQ;AAChB,mBAAW,QAAQ,SAAS,UAAU;AAAA,MACxC;AAEA,UAAI,MAAM,UAAU;AAClB,YAAI,QAAQ;AACV,oBAAU,QAAQ,SAAS,MAAM,UAAU;AAAA,QAC7C,OAAO;AACL,oBAAU,MAAM,UAAU;AAAA,QAC5B;AAAA,MACF,OAAO;AACL,kBAAU,SAAS,MAAM,UAAU;AAAA,MACrC;AAEA,eAAS;AAAA,IACX;AAAA,EACF;AAMA,MAAI,CAAC,QAAQ;AACX,aAAS,gBAAgB,MAAM,MAAM,GAAG,EAAE,IAAI,SAAS;AAAA,EACzD;AAEA,MAAI,KAAK;AACP,aAAS;AAAA,EACX,OAAO;AAGL,aAAS,UAAU,gBAAgB,KAAK;AAAA,EAC1C;AAEA,SAAO,IAAI,OAAO,MAAM,OAAO,MAAM,OAAO,CAAC;AAC/C;AAcA,SAAS,aAAc,MAAM,MAAM,SAAS;AAC1C,SAAO,QAAQ,CAAC;AAEhB,MAAI,CAAC,QAAQ,IAAI,GAAG;AAClB,cAAU;AACV,WAAO,CAAC;AAAA,EACV,WAAW,CAAC,SAAS;AACnB,cAAU,CAAC;AAAA,EACb;AAEA,MAAI,gBAAgB,QAAQ;AAC1B,WAAO,eAAe,MAAM,MAAM,OAAO;AAAA,EAC3C;AAEA,MAAI,QAAQ,IAAI,GAAG;AACjB,WAAO,cAAc,MAAM,MAAM,OAAO;AAAA,EAC1C;AAEA,SAAO,eAAe,MAAM,MAAM,OAAO;AAC3C;AAEA,eAAe,QAAQ;AACvB,eAAe,UAAU;AACzB,eAAe,mBAAmB;AAClC,eAAe,iBAAiB;AAY9B,IAAI,cAAe,gBAAgB,OAAO;AAC1C,IAAI,YAAa,gBAAgB,OAAO;AACxC,IAAI,aAAc,gBAAgB,OAAO;AACzC,IAAI,aAAa,OAAO,YAAY;AAKpC,IAAI,aAAa,eAAe,SAAS,eAAe,eAAe;AAOvE,IAAI,aAAa,aAAa,CAAC,EAAE,OAAO,QAAQ,YAAY,OAAO;AAMnE,SAAS,OAAO;AAEd,OAAK,YAAY,CAAC;AAClB,OAAK,QAAQ,CAAC;AACd,OAAK,UAAU;AACf,OAAK,MAAM;AAGX,OAAK,uBAAuB;AAC5B,OAAK,QAAQ;AACb,OAAK,UAAU;AACf,OAAK,WAAW;AAChB,OAAK,YAAY;AAGjB,OAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,OAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC/C;AASA,KAAK,UAAU,YAAY,SAAS,SAAS;AAC3C,MAAI,OAAO,WAAW,CAAC;AAEvB,OAAK,UAAU,KAAK,UAAW,aAAa;AAC5C,OAAK,uBAAuB,KAAK,wBAAwB;AACzD,OAAK,YAAY,KAAK,aAAa,SAAS;AAC5C,OAAK,SAAS,KAAK,UAAU,SAAS;AACtC,OAAK,YAAY,CAAC,CAAC,KAAK;AAExB,MAAI,UAAU,KAAK;AACnB,MAAG,KAAK,WAAW;AACjB,YAAQ,iBAAiB,YAAY,KAAK,aAAa,KAAK;AAAA,EAC9D,WAAU,WAAW;AACnB,YAAQ,oBAAoB,YAAY,KAAK,aAAa,KAAK;AAAA,EACjE;AAEA,MAAI,KAAK,QAAQ;AACf,YAAQ,SAAS,iBAAiB,YAAY,KAAK,cAAc,KAAK;AAAA,EACxE,WAAU,aAAa;AACrB,YAAQ,SAAS,oBAAoB,YAAY,KAAK,cAAc,KAAK;AAAA,EAC3E;AAEA,MAAG,KAAK,aAAa,aAAa,CAAC,YAAY;AAC7C,YAAQ,iBAAiB,cAAc,KAAK,aAAa,KAAK;AAAA,EAChE,WAAU,WAAW;AACnB,YAAQ,oBAAoB,cAAc,KAAK,aAAa,KAAK;AAAA,EACnE;AACF;AASA,KAAK,UAAU,OAAO,SAAS,MAAM;AACnC,MAAI,MAAM,UAAU,OAAQ,QAAO,KAAK;AACxC,OAAK,QAAQ;AACf;AAQA,KAAK,UAAU,WAAW,WAAW;AACnC,MAAI,OAAO,KAAK;AAChB,MAAG,CAAC,CAAC,KAAM,QAAO;AAClB,MAAI,MAAM,aAAa,KAAK,WAAW,KAAK,QAAQ;AAEpD,MAAG,aAAa,KAAK,aAAa,OAAO,IAAI,aAAa,SAAS;AACjE,WAAO,IAAI;AAAA,EACb;AAEA,SAAO;AACT;AASA,KAAK,UAAU,SAAS,SAAS,QAAQ;AACvC,MAAI,MAAM,UAAU,OAAQ,QAAO,KAAK;AACxC,OAAK,UAAU;AACjB;AAgBA,KAAK,UAAU,QAAQ,SAAS,SAAS;AACvC,MAAI,OAAO,WAAW,CAAC;AACvB,OAAK,UAAU,IAAI;AAEnB,MAAI,UAAU,KAAK,SAAU;AAC7B,OAAK,WAAW;AAEhB,MAAI;AACJ,MAAG,YAAY;AACb,QAAIC,UAAS,KAAK;AAClB,QAAI,MAAMA,QAAO;AAEjB,QAAG,KAAK,aAAa,CAAC,IAAI,KAAK,QAAQ,IAAI,GAAG;AAC5C,YAAM,IAAI,KAAK,OAAO,CAAC,IAAI,IAAI;AAAA,IACjC,WAAW,KAAK,WAAW;AACzB,YAAM,IAAI,SAAS,IAAI;AAAA,IACzB,OAAO;AACL,YAAM,IAAI,WAAW,IAAI,SAAS,IAAI;AAAA,IACxC;AAAA,EACF;AAEA,OAAK,QAAQ,KAAK,MAAM,MAAM,KAAK,QAAQ;AAC7C;AAQA,KAAK,UAAU,OAAO,WAAW;AAC/B,MAAI,CAAC,KAAK,SAAU;AACpB,OAAK,UAAU;AACf,OAAK,MAAM;AACX,OAAK,WAAW;AAEhB,MAAIA,UAAS,KAAK;AAClB,OAAK,UAAUA,QAAO,SAAS,oBAAoB,YAAY,KAAK,cAAc,KAAK;AACvF,eAAaA,QAAO,oBAAoB,YAAY,KAAK,aAAa,KAAK;AAC3E,eAAaA,QAAO,oBAAoB,cAAc,KAAK,aAAa,KAAK;AAC/E;AAaA,KAAK,UAAU,OAAO,SAAS,MAAM,OAAO,UAAU,MAAM;AAC1D,MAAI,MAAM,IAAI,QAAQ,MAAM,OAAO,IAAI,GACrC,OAAO,KAAK;AACd,OAAK,cAAc;AACnB,OAAK,UAAU,IAAI;AACnB,MAAI,UAAU,SAAU,MAAK,SAAS,KAAK,IAAI;AAC/C,MAAI,UAAU,IAAI,WAAW,UAAU,KAAM,KAAI,UAAU;AAC3D,SAAO;AACT;AAWA,KAAK,UAAU,OAAO,SAAS,MAAM,OAAO;AAC1C,MAAIC,QAAO;AACX,MAAI,KAAK,MAAM,GAAG;AAChB,QAAID,UAAS,KAAK;AAGlB,kBAAcA,QAAO,QAAQ,KAAK;AAClC,SAAK;AAAA,EACP,WAAW,MAAM;AACf,eAAW,WAAW;AACpB,MAAAC,MAAK,KAAK,MAAM,KAAK;AAAA,IACvB,CAAC;AAAA,EACH,OAAO;AACL,eAAW,WAAW;AACpB,MAAAA,MAAK,KAAKA,MAAK,SAAS,GAAG,KAAK;AAAA,IAClC,CAAC;AAAA,EACH;AACF;AAUA,KAAK,UAAU,WAAW,SAAS,MAAM,IAAI;AAC3C,MAAI,OAAO;AAGX,MAAI,aAAa,OAAO,QAAQ,aAAa,OAAO,IAAI;AACtD,SAAK,KAAK,MAAM,MAAM,SAAS,GAAG;AAChC,iBAAW,WAAW;AACpB,aAAK;AAAA;AAAA,UAAgC;AAAA,QAAG;AAAA,MAC1C,GAAG,CAAC;AAAA,IACN,CAAC;AAAA,EACH;AAGA,MAAI,aAAa,OAAO,QAAQ,gBAAgB,OAAO,IAAI;AACzD,eAAW,WAAW;AACpB,WAAK,QAAQ,IAAI;AAAA,IACnB,GAAG,CAAC;AAAA,EACN;AACF;AAcA,KAAK,UAAU,UAAU,SAAS,MAAM,OAAO,MAAM,UAAU;AAC7D,MAAI,MAAM,IAAI,QAAQ,MAAM,OAAO,IAAI,GACrC,OAAO,KAAK;AACd,OAAK,cAAc;AACnB,OAAK,UAAU,IAAI;AACnB,MAAI,OAAO;AACX,MAAI,KAAK;AACT,MAAI,UAAU,SAAU,MAAK,SAAS,KAAK,IAAI;AAC/C,SAAO;AACT;AASA,KAAK,UAAU,WAAW,SAAS,KAAK,MAAM;AAC5C,MAAI,IAAI,GAAG,IAAI,GAAGA,QAAO;AAEzB,WAAS,WAAW;AAClB,QAAI,KAAKA,MAAK,MAAM,GAAG;AACvB,QAAI,CAAC,GAAI,QAAO,UAAU;AAC1B,OAAG,MAAM,QAAQ;AAAA,EACnB;AAEA,WAAS,YAAY;AACnB,QAAI,KAAKA,MAAK,UAAU,GAAG;AAE3B,QAAI,IAAI,SAASA,MAAK,SAAS;AAC7B,UAAI,UAAU;AACd;AAAA,IACF;AACA,QAAI,CAAC,GAAI,QAAO,UAAU,KAAKA,OAAM,GAAG;AACxC,OAAG,KAAK,SAAS;AAAA,EACnB;AAEA,MAAI,MAAM;AACR,aAAS;AAAA,EACX,OAAO;AACL,cAAU;AAAA,EACZ;AACF;AAQA,KAAK,UAAU,OAAO,SAAS,MAAM,IAAI;AACvC,MAAI,OAAO,SAAS,YAAY;AAC9B,WAAO,KAAK,KAAK,KAAK,IAAI;AAAA,EAC5B;AAEA,MAAI,QAAQ,IAAI,MAAM,MAAM,MAAM,IAAI;AACtC,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACzC,SAAK,MAAM,KAAK,MAAM,WAAW,UAAU,CAAC,CAAC,CAAC;AAAA,EAChD;AACF;AAOA,KAAK,UAAU,eAAe,SAAS,GAAG;AACxC,MAAI,MAAM,KAAK,OAAO,CAAC,EAAG;AAE1B,MAAI,EAAE,WAAW,EAAE,WAAW,EAAE,SAAU;AAC1C,MAAI,EAAE,iBAAkB;AAKxB,MAAI,KAAK,EAAE;AACX,MAAI,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,IAAI;AAE/D,MAAG,WAAW;AACZ,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,CAAC,UAAU,CAAC,EAAE,SAAU;AAC5B,UAAI,UAAU,CAAC,EAAE,SAAS,YAAY,MAAM,IAAK;AACjD,UAAI,CAAC,UAAU,CAAC,EAAE,KAAM;AAExB,WAAK,UAAU,CAAC;AAChB;AAAA,IACF;AAAA,EACF;AAIA,SAAO,MAAM,QAAQ,GAAG,SAAS,YAAY,EAAG,MAAK,GAAG;AACxD,MAAI,CAAC,MAAM,QAAQ,GAAG,SAAS,YAAY,EAAG;AAI9C,MAAI,MAAO,OAAO,GAAG,SAAS,YAAa,GAAG,KAAK,YAAY,SAAS;AAKxE,MAAI,GAAG,aAAa,UAAU,KAAK,GAAG,aAAa,KAAK,MAAM,WAAY;AAG1E,MAAI,OAAO,GAAG,aAAa,MAAM;AACjC,MAAG,CAAC,KAAK,aAAa,KAAK,UAAU,EAAE,MAAM,GAAG,QAAQ,QAAQ,MAAO;AAGvE,MAAI,QAAQ,KAAK,QAAQ,SAAS,IAAI,GAAI;AAI1C,MAAI,MAAM,GAAG,OAAO,UAAU,GAAG,OAAQ;AAKzC,MAAI,CAAC,OAAO,CAAC,KAAK,WAAW,GAAG,IAAI,EAAG;AAKvC,MAAI,OAAO,MAAM,GAAG,KAAK,UAAW,GAAG,WAAW,GAAG,UAAU,GAAG,QAAQ;AAE1E,SAAO,KAAK,CAAC,MAAM,MAAM,MAAM,OAAO;AAGtC,MAAI,cAAc,KAAK,MAAM,gBAAgB,GAAG;AAC9C,WAAO,KAAK,QAAQ,kBAAkB,GAAG;AAAA,EAC3C;AAGA,MAAI,OAAO;AACX,MAAI,WAAW,KAAK,SAAS;AAE7B,MAAI,KAAK,QAAQ,QAAQ,MAAM,GAAG;AAChC,WAAO,KAAK,OAAO,SAAS,MAAM;AAAA,EACpC;AAEA,MAAI,KAAK,UAAW,QAAO,KAAK,QAAQ,MAAM,EAAE;AAEhD,MAAI,YAAY,SAAS,SAAS,CAAC,cAAc,KAAK,QAAQ,SAAS,aAAa,UAAU;AAC5F;AAAA,EACF;AAEA,IAAE,eAAe;AACjB,OAAK,KAAK,IAAI;AAChB;AAOA,KAAK,UAAU,cAAe,WAAY;AACxC,MAAI,SAAS;AACb,MAAK,CAAE,WAAY;AACjB,WAAO,WAAY;AAAA,IAAC;AAAA,EACtB;AACA,MAAI,eAAe,SAAS,eAAe,YAAY;AACrD,aAAS;AAAA,EACX,OAAO;AACL,WAAO,iBAAiB,QAAQ,WAAW;AACzC,iBAAW,WAAW;AACpB,iBAAS;AAAA,MACX,GAAG,CAAC;AAAA,IACN,CAAC;AAAA,EACH;AACA,SAAO,SAAS,WAAW,GAAG;AAC5B,QAAI,CAAC,OAAQ;AACb,QAAIA,QAAO;AACX,QAAI,EAAE,OAAO;AACX,UAAI,OAAO,EAAE,MAAM;AACnB,MAAAA,MAAK,QAAQ,MAAM,EAAE,KAAK;AAAA,IAC5B,WAAW,YAAY;AACrB,UAAI,MAAMA,MAAK,QAAQ;AACvB,MAAAA,MAAK,KAAK,IAAI,WAAW,IAAI,SAAS,IAAI,MAAM,QAAW,QAAW,KAAK;AAAA,IAC7E;AAAA,EACF;AACF,EAAG;AAKH,KAAK,UAAU,SAAS,SAAS,GAAG;AAClC,MAAI,KAAM,aAAa,KAAK,QAAQ;AACpC,SAAO,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE;AACxC;AAMA,KAAK,UAAU,SAAS,SAAS,MAAM;AACrC,MAAID,UAAS,KAAK;AAClB,MAAG,OAAO,QAAQ,cAAc,YAAY;AAC1C,WAAO,IAAI,IAAI,MAAMA,QAAO,SAAS,SAAS,CAAC;AAAA,EACjD,WAAW,aAAa;AACtB,QAAI,MAAMA,QAAO,SAAS,cAAc,GAAG;AAC3C,QAAI,OAAO;AACX,WAAO;AAAA,EACT;AACF;AAOA,KAAK,UAAU,aAAa,SAAS,MAAM;AACzC,MAAG,CAAC,QAAQ,CAAC,WAAY,QAAO;AAEhC,MAAI,MAAM,KAAK,OAAO,IAAI;AAC1B,MAAIA,UAAS,KAAK;AAElB,MAAI,MAAMA,QAAO;AAUjB,SAAO,IAAI,aAAa,IAAI,YAC1B,IAAI,aAAa,IAAI,aACpB,IAAI,SAAS,IAAI,QAAQ,IAAI,SAAS,OAAO,IAAI,QAAQ,MAAM,IAAI,QAAQ;AAChF;AAKA,KAAK,UAAU,YAAY,SAAS,KAAK;AACvC,MAAG,CAAC,WAAY,QAAO;AACvB,MAAIA,UAAS,KAAK;AAClB,MAAI,MAAMA,QAAO;AACjB,SAAO,IAAI,aAAa,IAAI,YAC1B,IAAI,WAAW,IAAI;AACvB;AAUA,KAAK,UAAU,gCAAgC,SAAS,KAAK;AAC3D,MAAI,OAAO,QAAQ,UAAU;AAAE,WAAO;AAAA,EAAK;AAC3C,SAAO,KAAK,uBAAuB,mBAAmB,IAAI,QAAQ,OAAO,GAAG,CAAC,IAAI;AACnF;AAKA,SAAS,aAAa;AACpB,MAAI,eAAe,IAAI,KAAK;AAE5B,WAAS,SAAmB;AAC1B,WAAO,KAAK,MAAM,cAAc,SAAS;AAAA,EAC3C;AAGA,SAAO,YAAY,aAAa;AAChC,SAAO,QAAQ,aAAa;AAC5B,SAAO,OAAO,aAAa,KAAK,KAAK,YAAY;AACjD,SAAO,SAAS,aAAa,OAAO,KAAK,YAAY;AACrD,SAAO,QAAQ,aAAa,MAAM,KAAK,YAAY;AACnD,SAAO,OAAO,aAAa,KAAK,KAAK,YAAY;AACjD,SAAO,OAAO,aAAa,KAAK,KAAK,YAAY;AACjD,SAAO,OAAO,aAAa,KAAK,KAAK,YAAY;AACjD,SAAO,WAAW,aAAa,SAAS,KAAK,YAAY;AACzD,SAAO,UAAU,aAAa,QAAQ,KAAK,YAAY;AACvD,SAAO,WAAW,aAAa,SAAS,KAAK,YAAY;AACzD,SAAO,OAAO,aAAa,KAAK,KAAK,YAAY;AACjD,SAAO,YAAY,aAAa,UAAU,KAAK,YAAY;AAC3D,SAAO,aAAa,aAAa,WAAW,KAAK,YAAY;AAC7D,SAAO,eAAe,aAAa,aAAa,KAAK,YAAY;AAEjE,SAAO,SAAS;AAEhB,SAAO,eAAe,QAAQ,OAAO;AAAA,IACnC,KAAK,WAAU;AACb,aAAO,aAAa;AAAA,IACtB;AAAA,IACA,KAAK,SAAS,KAAK;AACjB,mBAAa,MAAM;AAAA,IACrB;AAAA,EACF,CAAC;AAED,SAAO,eAAe,QAAQ,WAAW;AAAA,IACvC,KAAK,WAAU;AACb,aAAO,aAAa;AAAA,IACtB;AAAA,IACA,KAAK,SAAS,KAAK;AACjB,mBAAa,UAAU;AAAA,IACzB;AAAA,EACF,CAAC;AAGD,SAAO,UAAU;AACjB,SAAO,QAAQ;AAEf,SAAO;AACT;AAoBA,SAAS,KAAK,MAAM,IAAI;AAEtB,MAAI,eAAe,OAAO,MAAM;AAC9B,WAAO,KAAK,KAAK,MAAM,KAAK,IAAI;AAAA,EAClC;AAGA,MAAI,eAAe,OAAO,IAAI;AAC5B,QAAI,QAAQ,IAAI;AAAA;AAAA,MAA6B;AAAA,MAAO;AAAA,MAAM;AAAA,IAAI;AAC9D,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,EAAE,GAAG;AACzC,WAAK,UAAU,KAAK,MAAM,WAAW,UAAU,CAAC,CAAC,CAAC;AAAA,IACpD;AAAA,EAEF,WAAW,aAAa,OAAO,MAAM;AACnC,SAAK,aAAa,OAAO,KAAK,aAAa,MAAM,EAAE,MAAM,EAAE;AAAA,EAE7D,OAAO;AACL,SAAK,MAAM,IAAI;AAAA,EACjB;AACF;AAUA,SAAS,UAAU,KAAK;AACtB,MAAI,IAAI,QAAS;AACjB,MAAI;AACJ,MAAIC,QAAO;AACX,MAAID,UAASC,MAAK;AAElB,MAAIA,MAAK,WAAW;AAClB,cAAU,cAAc,KAAK,SAAS,IAAID,QAAO,SAAS,KAAK,QAAQ,MAAM,EAAE;AAAA,EACjF,OAAO;AACL,cAAU,cAAcA,QAAO,SAAS,WAAWA,QAAO,SAAS;AAAA,EACrE;AAEA,MAAI,YAAY,IAAI,cAAe;AACnC,EAAAC,MAAK,KAAK;AACV,MAAI,UAAU;AACd,iBAAeD,QAAO,SAAS,OAAO,IAAI;AAC5C;AAQA,SAAS,aAAa,GAAG;AACvB,SAAO,EAAE,QAAQ,6BAA6B,MAAM;AACtD;AAYA,SAAS,QAAQ,MAAM,OAAO,cAAc;AAC1C,MAAI,QAAQ,KAAK,OAAO,gBAAgB;AACxC,MAAIA,UAAS,MAAM;AACnB,MAAI,WAAW,MAAM;AAErB,MAAI,WAAW,MAAM,SAAS;AAC9B,MAAI,QAAQ,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,QAAQ,EAAG,QAAO,YAAY,WAAW,OAAO,MAAM;AAChG,MAAI,IAAI,KAAK,QAAQ,GAAG;AAExB,OAAK,gBAAgB;AACrB,MAAI,KAAK,IAAI,OAAO,MAAM,aAAa,QAAQ,CAAC;AAChD,OAAK,OAAO,KAAK,QAAQ,IAAI,EAAE,KAAK;AACpC,MAAI,SAAU,MAAK,OAAO,KAAK,KAAK,QAAQ,MAAM,EAAE,KAAK;AAEzD,OAAK,QAAS,eAAeA,QAAO,SAAS;AAC7C,OAAK,QAAQ,SAAS,CAAC;AACvB,OAAK,MAAM,OAAO;AAClB,OAAK,cAAc,CAAC,IAAI,MAAM,8BAA8B,KAAK,MAAM,IAAI,CAAC,CAAC,IAAI;AACjF,OAAK,WAAW,MAAM,8BAA8B,CAAC,IAAI,KAAK,MAAM,GAAG,CAAC,IAAI,IAAI;AAChF,OAAK,SAAS,CAAC;AAGf,OAAK,OAAO;AACZ,MAAI,CAAC,UAAU;AACb,QAAI,CAAC,CAAC,KAAK,KAAK,QAAQ,GAAG,EAAG;AAC9B,QAAI,QAAQ,KAAK,KAAK,MAAM,GAAG;AAC/B,SAAK,OAAO,KAAK,WAAW,MAAM,CAAC;AACnC,SAAK,OAAO,MAAM,8BAA8B,MAAM,CAAC,CAAC,KAAK;AAC7D,SAAK,cAAc,KAAK,YAAY,MAAM,GAAG,EAAE,CAAC;AAAA,EAClD;AACF;AAQA,QAAQ,UAAU,YAAY,WAAW;AACvC,MAAIC,QAAO,KAAK;AAChB,MAAID,UAASC,MAAK;AAClB,MAAI,WAAWA,MAAK;AAEpB,EAAAA,MAAK;AACL,MAAI,YAAY;AACZ,IAAAD,QAAO,QAAQ;AAAA,MAAU,KAAK;AAAA,MAAO,KAAK;AAAA,MACxC,YAAY,KAAK,SAAS,MAAM,OAAO,KAAK,OAAO,KAAK;AAAA,IAAa;AAAA,EAC3E;AACF;AAQA,QAAQ,UAAU,OAAO,WAAW;AAClC,MAAIC,QAAO,KAAK;AAChB,MAAI,YAAY;AACZ,IAAAA,MAAK,QAAQ,QAAQ;AAAA,MAAa,KAAK;AAAA,MAAO,KAAK;AAAA,MACjDA,MAAK,aAAa,KAAK,SAAS,MAAM,OAAO,KAAK,OAAO,KAAK;AAAA,IAAa;AAAA,EACjF;AACF;AAiBA,SAAS,MAAM,MAAM,SAASA,OAAM;AAClC,MAAI,QAAQ,KAAK,OAAOA,SAAQ;AAChC,MAAI,OAAO,WAAW,CAAC;AACvB,OAAK,SAAS,KAAK,UAAU,MAAM;AACnC,OAAK,OAAQ,SAAS,MAAO,SAAS;AACtC,OAAK,SAAS;AACd,OAAK,SAAS,eAAe,KAAK,MAAM,KAAK,OAAO,CAAC,GAAG,IAAI;AAC9D;AAWA,MAAM,UAAU,aAAa,SAAS,IAAI;AACxC,MAAI,OAAO;AACX,SAAO,SAAS,KAAK,MAAM;AACzB,QAAI,KAAK,MAAM,IAAI,MAAM,IAAI,MAAM,GAAG;AACpC,UAAI,YAAY,KAAK;AACrB,aAAO,GAAG,KAAK,IAAI;AAAA,IACrB;AACA,SAAK;AAAA,EACP;AACF;AAYA,MAAM,UAAU,QAAQ,SAAS,MAAM,QAAQ;AAC7C,MAAI,OAAO,KAAK,MACd,UAAU,KAAK,QAAQ,GAAG,GAC1B,WAAW,CAAC,UAAU,KAAK,MAAM,GAAG,OAAO,IAAI,MAC/C,IAAI,KAAK,OAAO,KAAK,mBAAmB,QAAQ,CAAC;AAEnD,MAAI,CAAC,EAAG,QAAO;AAEf,SAAO,OAAO,CAAC;AAEf,WAAS,IAAI,GAAG,MAAM,EAAE,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC5C,QAAI,MAAM,KAAK,IAAI,CAAC;AACpB,QAAI,MAAM,KAAK,KAAK,8BAA8B,EAAE,CAAC,CAAC;AACtD,QAAI,QAAQ,UAAa,CAAE,eAAe,KAAK,QAAQ,IAAI,IAAI,GAAI;AACjE,aAAO,IAAI,IAAI,IAAI;AAAA,IACrB;AAAA,EACF;AAEA,SAAO;AACT;AAOA,IAAI,aAAa,WAAW;AAC5B,IAAI,UAAU;AACd,IAAI,YAAY;AAElB,QAAQ,UAAU;AAElB,IAAO,eAAQ;", "names": ["i", "window", "page"]}