// node_modules/esm-env/true.js
var true_default = true;

// node_modules/svelte/src/internal/client/warnings.js
var bold = "font-weight: bold";
var normal = "font-weight: normal";
function assignment_value_stale(property, location) {
  if (true_default) {
    console.warn(`%c[svelte] assignment_value_stale
%cAssignment to \`${property}\` property (${location}) will evaluate to the right-hand side, not the value of \`${property}\` following the assignment. This may result in unexpected behaviour.
https://svelte.dev/e/assignment_value_stale`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/assignment_value_stale`);
  }
}
function await_reactivity_loss(name) {
  if (true_default) {
    console.warn(`%c[svelte] await_reactivity_loss
%cDetected reactivity loss when reading \`${name}\`. This happens when state is read in an async function after an earlier \`await\`
https://svelte.dev/e/await_reactivity_loss`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/await_reactivity_loss`);
  }
}
function await_waterfall(name, location) {
  if (true_default) {
    console.warn(`%c[svelte] await_waterfall
%cAn async derived, \`${name}\` (${location}) was not read immediately after it resolved. This often indicates an unnecessary waterfall, which can slow down your app
https://svelte.dev/e/await_waterfall`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/await_waterfall`);
  }
}
function binding_property_non_reactive(binding, location) {
  if (true_default) {
    console.warn(
      `%c[svelte] binding_property_non_reactive
%c${location ? `\`${binding}\` (${location}) is binding to a non-reactive property` : `\`${binding}\` is binding to a non-reactive property`}
https://svelte.dev/e/binding_property_non_reactive`,
      bold,
      normal
    );
  } else {
    console.warn(`https://svelte.dev/e/binding_property_non_reactive`);
  }
}
function console_log_state(method) {
  if (true_default) {
    console.warn(`%c[svelte] console_log_state
%cYour \`console.${method}\` contained \`$state\` proxies. Consider using \`$inspect(...)\` or \`$state.snapshot(...)\` instead
https://svelte.dev/e/console_log_state`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/console_log_state`);
  }
}
function event_handler_invalid(handler, suggestion) {
  if (true_default) {
    console.warn(`%c[svelte] event_handler_invalid
%c${handler} should be a function. Did you mean to ${suggestion}?
https://svelte.dev/e/event_handler_invalid`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/event_handler_invalid`);
  }
}
function hydration_attribute_changed(attribute, html, value) {
  if (true_default) {
    console.warn(`%c[svelte] hydration_attribute_changed
%cThe \`${attribute}\` attribute on \`${html}\` changed its value between server and client renders. The client value, \`${value}\`, will be ignored in favour of the server value
https://svelte.dev/e/hydration_attribute_changed`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/hydration_attribute_changed`);
  }
}
function hydration_html_changed(location) {
  if (true_default) {
    console.warn(
      `%c[svelte] hydration_html_changed
%c${location ? `The value of an \`{@html ...}\` block ${location} changed between server and client renders. The client value will be ignored in favour of the server value` : "The value of an `{@html ...}` block changed between server and client renders. The client value will be ignored in favour of the server value"}
https://svelte.dev/e/hydration_html_changed`,
      bold,
      normal
    );
  } else {
    console.warn(`https://svelte.dev/e/hydration_html_changed`);
  }
}
function hydration_mismatch(location) {
  if (true_default) {
    console.warn(
      `%c[svelte] hydration_mismatch
%c${location ? `Hydration failed because the initial UI does not match what was rendered on the server. The error occurred near ${location}` : "Hydration failed because the initial UI does not match what was rendered on the server"}
https://svelte.dev/e/hydration_mismatch`,
      bold,
      normal
    );
  } else {
    console.warn(`https://svelte.dev/e/hydration_mismatch`);
  }
}
function invalid_raw_snippet_render() {
  if (true_default) {
    console.warn(`%c[svelte] invalid_raw_snippet_render
%cThe \`render\` function passed to \`createRawSnippet\` should return HTML for a single element
https://svelte.dev/e/invalid_raw_snippet_render`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/invalid_raw_snippet_render`);
  }
}
function legacy_recursive_reactive_block(filename) {
  if (true_default) {
    console.warn(`%c[svelte] legacy_recursive_reactive_block
%cDetected a migrated \`$:\` reactive block in \`${filename}\` that both accesses and updates the same reactive value. This may cause recursive updates when converted to an \`$effect\`.
https://svelte.dev/e/legacy_recursive_reactive_block`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/legacy_recursive_reactive_block`);
  }
}
function lifecycle_double_unmount() {
  if (true_default) {
    console.warn(`%c[svelte] lifecycle_double_unmount
%cTried to unmount a component that was not mounted
https://svelte.dev/e/lifecycle_double_unmount`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/lifecycle_double_unmount`);
  }
}
function ownership_invalid_binding(parent, prop, child, owner) {
  if (true_default) {
    console.warn(`%c[svelte] ownership_invalid_binding
%c${parent} passed property \`${prop}\` to ${child} with \`bind:\`, but its parent component ${owner} did not declare \`${prop}\` as a binding. Consider creating a binding between ${owner} and ${parent} (e.g. \`bind:${prop}={...}\` instead of \`${prop}={...}\`)
https://svelte.dev/e/ownership_invalid_binding`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/ownership_invalid_binding`);
  }
}
function ownership_invalid_mutation(name, location, prop, parent) {
  if (true_default) {
    console.warn(`%c[svelte] ownership_invalid_mutation
%cMutating unbound props (\`${name}\`, at ${location}) is strongly discouraged. Consider using \`bind:${prop}={...}\` in ${parent} (or using a callback) instead
https://svelte.dev/e/ownership_invalid_mutation`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/ownership_invalid_mutation`);
  }
}
function select_multiple_invalid_value() {
  if (true_default) {
    console.warn(`%c[svelte] select_multiple_invalid_value
%cThe \`value\` property of a \`<select multiple>\` element should be an array, but it received a non-array value. The selection will be kept as is.
https://svelte.dev/e/select_multiple_invalid_value`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/select_multiple_invalid_value`);
  }
}
function state_proxy_equality_mismatch(operator) {
  if (true_default) {
    console.warn(`%c[svelte] state_proxy_equality_mismatch
%cReactive \`$state(...)\` proxies and the values they proxy have different identities. Because of this, comparisons with \`${operator}\` will produce unexpected results
https://svelte.dev/e/state_proxy_equality_mismatch`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/state_proxy_equality_mismatch`);
  }
}
function svelte_boundary_reset_noop() {
  if (true_default) {
    console.warn(`%c[svelte] svelte_boundary_reset_noop
%cA \`<svelte:boundary>\` \`reset\` function only resets the boundary the first time it is called
https://svelte.dev/e/svelte_boundary_reset_noop`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/svelte_boundary_reset_noop`);
  }
}
function transition_slide_display(value) {
  if (true_default) {
    console.warn(`%c[svelte] transition_slide_display
%cThe \`slide\` transition does not work correctly for elements with \`display: ${value}\`
https://svelte.dev/e/transition_slide_display`, bold, normal);
  } else {
    console.warn(`https://svelte.dev/e/transition_slide_display`);
  }
}

export {
  true_default,
  assignment_value_stale,
  await_reactivity_loss,
  await_waterfall,
  binding_property_non_reactive,
  console_log_state,
  event_handler_invalid,
  hydration_attribute_changed,
  hydration_html_changed,
  hydration_mismatch,
  invalid_raw_snippet_render,
  legacy_recursive_reactive_block,
  lifecycle_double_unmount,
  ownership_invalid_binding,
  ownership_invalid_mutation,
  select_multiple_invalid_value,
  state_proxy_equality_mismatch,
  svelte_boundary_reset_noop,
  transition_slide_display
};
//# sourceMappingURL=chunk-QPV5QCCE.js.map
