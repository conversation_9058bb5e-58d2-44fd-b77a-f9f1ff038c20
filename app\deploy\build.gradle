plugins {
    id 'base'
}

ext.SERVER_NAME = "server"
ext.SERVER_IMAGE = rootProject.name + "/server"
ext.CLIENT_NAME = "server"                  
ext.CLIENT_IMAGE = rootProject.name + "/server"

tasks.register('composeUp') {
    mustRunAfter ':app:client:buildImage'
    mustRunAfter ':app:server:buildImage'
    doLast {
        println '\nDeploying with latest'
        exec {
            workingDir "${projectDir}"
            executable 'docker'
            args 'compose', '-p', 'petclinic', '-f', 'compose.yml', 'up', '--detach', '--no-build'
        }
    }
}

tasks.register('composeStop') {
    doLast {
        exec {            
            workingDir "${projectDir}"
            executable 'docker'
            args 'compose', '-p', 'petclinic', '-f', 'compose.yml', 'stop'
        }
    }
}

tasks.register('composeDown') {
    doLast {
        exec {
            workingDir "${projectDir}"
            executable 'docker'
            args 'compose', '-p', 'petclinic', '-f', 'compose.yml', 'down'
        }
    }
}

tasks.register('versionCheck', VersionCheckTask.class)  {
    group 'verification'
    allFileWithVersion.from("Chart.yaml")
}

tasks.register('lint') {
    group = 'verification'
}

tasks.register('format') {
    group = 'build'
}
